class KashfREPL {
    constructor() {
        this.socket = null;
        this.editor = null;
        this.isConnected = false;
        this.executionHistory = [];
        this.currentLayout = 'horizontal';
        this.isResizing = false;
        this.sidebarVisible = true;

        this.init();
    }

    init() {
        this.initializeEditor();
        this.connectWebSocket();
        this.setupEventListeners();
        this.setupResizer();

        // Load saved code if available
        setTimeout(() => {
            this.loadFromLocalStorage();
        }, 100);
    }

    initializeEditor() {
        this.editor = CodeMirror.fromTextArea(document.getElementById('code-editor'), {
            mode: 'javascript',
            theme: 'monokai',
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            styleActiveLine: true,
            showTrailingSpace: true,
            indentUnit: 4,
            tabSize: 4,
            lineWrapping: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
            extraKeys: {
                'Ctrl-Enter': () => this.executeCode(),
                'Ctrl-/': 'toggleComment',
                'Ctrl-L': () => this.clearResults(),
                'Ctrl-K': () => this.clearEditor(),
                'Ctrl-D': () => this.duplicateLine(),
                'Ctrl-F': 'findPersistent',
                'Ctrl-H': 'replace',
                'Tab': 'autocomplete',
                'Ctrl-Space': 'autocomplete',
                'Alt-Up': () => this.moveLineUp(),
                'Alt-Down': () => this.moveLineDown(),
                'Ctrl-]': 'indentMore',
                'Ctrl-[': 'indentLess',
                'Ctrl-A': 'selectAll',
                'Home': 'goLineStartSmart',
                'End': 'goLineEnd',
                'Ctrl-Home': 'goDocStart',
                'Ctrl-End': 'goDocEnd'
            },
            hintOptions: {
                completeSingle: false,
                hint: CodeMirror.hint.javascript
            }
        });

        // Auto-resize editor
        this.editor.setSize(null, 250);

        // Focus the editor
        this.editor.focus();

        // Add custom styling for better UX
        this.addEditorEnhancements();

        // Add event listeners for better UX
        this.setupEditorEventListeners();
    }

    addEditorEnhancements() {
        // Add custom CSS for better visual feedback
        const style = document.createElement('style');
        style.textContent = `
            .CodeMirror-activeline-background {
                background: rgba(255, 255, 255, 0.1) !important;
            }
            .cm-trailingspace {
                background-color: rgba(255, 0, 0, 0.3);
            }
            .CodeMirror-foldgutter-open,
            .CodeMirror-foldgutter-folded {
                cursor: pointer;
                color: #999;
            }
            .CodeMirror-foldgutter-open:hover,
            .CodeMirror-foldgutter-folded:hover {
                color: #fff;
            }
            .CodeMirror-focused .CodeMirror-selected {
                background: rgba(255, 255, 255, 0.2);
            }
        `;
        document.head.appendChild(style);
    }

    duplicateLine() {
        const cursor = this.editor.getCursor();
        const line = this.editor.getLine(cursor.line);
        this.editor.replaceRange('\n' + line, { line: cursor.line, ch: line.length });
        this.editor.setCursor({ line: cursor.line + 1, ch: cursor.ch });
    }

    moveLineUp() {
        const cursor = this.editor.getCursor();
        if (cursor.line === 0) return;

        const currentLine = this.editor.getLine(cursor.line);
        const prevLine = this.editor.getLine(cursor.line - 1);

        this.editor.replaceRange(currentLine + '\n' + prevLine,
            { line: cursor.line - 1, ch: 0 },
            { line: cursor.line + 1, ch: 0 });

        this.editor.setCursor({ line: cursor.line - 1, ch: cursor.ch });
    }

    moveLineDown() {
        const cursor = this.editor.getCursor();
        const lastLine = this.editor.lastLine();
        if (cursor.line === lastLine) return;

        const currentLine = this.editor.getLine(cursor.line);
        const nextLine = this.editor.getLine(cursor.line + 1);

        this.editor.replaceRange(nextLine + '\n' + currentLine,
            { line: cursor.line, ch: 0 },
            { line: cursor.line + 2, ch: 0 });

        this.editor.setCursor({ line: cursor.line + 1, ch: cursor.ch });
    }

    setupEditorEventListeners() {
        // Add change event listener for auto-save or other features
        this.editor.on('change', () => {
            // Could implement auto-save to localStorage here
            this.saveToLocalStorage();
        });

        // Add cursor activity listener for better UX
        this.editor.on('cursorActivity', () => {
            // Update status bar with cursor position
            this.updateCursorPosition();
        });

        // Add focus/blur listeners
        this.editor.on('focus', () => {
            document.querySelector('.code-section').style.borderColor = 'rgba(102, 126, 234, 0.5)';
        });

        this.editor.on('blur', () => {
            document.querySelector('.code-section').style.borderColor = 'rgba(255, 255, 255, 0.1)';
        });
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('kashf-repl-code', this.editor.getValue());
        } catch (e) {
            // Ignore localStorage errors
        }
    }

    loadFromLocalStorage() {
        try {
            const savedCode = localStorage.getItem('kashf-repl-code');
            const currentCode = this.editor.getValue();
            const isDefaultCode = currentCode.includes('Welcome to Kashf REPL!') || currentCode.trim() === 'help()';

            if (savedCode && savedCode.trim() !== '' && savedCode !== currentCode) {
                if (isDefaultCode || confirm('Load previously saved code? This will replace current content.')) {
                    this.editor.setValue(savedCode);
                    this.showTemporaryMessage('Saved code loaded!', 'success');
                }
            }
        } catch (e) {
            // Ignore localStorage errors
        }
    }

    updateCursorPosition() {
        const cursor = this.editor.getCursor();
        const statusElement = document.getElementById('cursor-position');
        if (statusElement) {
            statusElement.textContent = `Line ${cursor.line + 1}, Column ${cursor.ch + 1}`;
        }
    }

    formatCode() {
        try {
            const code = this.editor.getValue();
            if (!code.trim()) return;

            // Basic JavaScript formatting
            const formatted = this.basicJSFormat(code);
            this.editor.setValue(formatted);

            // Show feedback
            this.showTemporaryMessage('Code formatted!', 'success');
        } catch (error) {
            this.showTemporaryMessage('Failed to format code: ' + error.message, 'error');
        }
    }

    basicJSFormat(code) {
        // Basic formatting - add proper indentation and spacing
        let formatted = code
            .replace(/;/g, ';\n')
            .replace(/\{/g, ' {\n')
            .replace(/\}/g, '\n}\n')
            .replace(/,/g, ',\n')
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        let indentLevel = 0;
        const indentSize = 4;

        return formatted.map(line => {
            if (line.includes('}')) indentLevel = Math.max(0, indentLevel - 1);
            const indentedLine = ' '.repeat(indentLevel * indentSize) + line;
            if (line.includes('{')) indentLevel++;
            return indentedLine;
        }).join('\n');
    }

    showTemporaryMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            background: ${type === 'success' ? '#2ed573' : type === 'error' ? '#ff4757' : '#ffa502'};
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 2000);
    }

    connectWebSocket() {
        // For now, use REST API instead of WebSocket
        this.checkConnection();
        this.getContextInfo();
    }

    async checkConnection() {
        try {
            const response = await fetch('/api/context');
            if (response.ok) {
                this.isConnected = true;
                this.updateConnectionStatus(true);
                console.log('Connected to Kashf REPL server');
            } else {
                throw new Error('Server not responding');
            }
        } catch (error) {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            console.error('Connection error:', error);
        }
    }

    setupEventListeners() {
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.executeCode();
            }
        });

        // Button events
        document.getElementById('execute-btn').addEventListener('click', () => this.executeCode());
    }

    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-status');
        const statusText = document.getElementById('status-text');
        
        if (connected) {
            indicator.classList.add('connected');
            statusText.textContent = 'Connected';
        } else {
            indicator.classList.remove('connected');
            statusText.textContent = 'Disconnected';
        }
    }

    updateContextInfo(info) {
        const contextDiv = document.getElementById('context-info');
        
        if (info.error) {
            contextDiv.innerHTML = `<p style="color: #ff4757;">Error: ${info.error}</p>`;
            return;
        }

        let html = '';
        
        if (info.availableServices) {
            html += `<p><strong>Services:</strong> ${info.availableServices}</p>`;
        }
        
        if (info.contextKeys && info.contextKeys.length > 0) {
            html += `<p><strong>Available:</strong></p><ul>`;
            info.contextKeys.forEach(key => {
                html += `<li>${key}</li>`;
            });
            html += `</ul>`;
        }
        
        if (info.helpAvailable) {
            html += `<p><em>Type <code>help()</code> for more info</em></p>`;
        }
        
        contextDiv.innerHTML = html || '<p>No context information available</p>';
    }

    async executeCode() {
        if (!this.isConnected) {
            this.showResult({
                success: false,
                error: 'Not connected to REPL server'
            });
            return;
        }

        const code = this.editor.getValue().trim();
        if (!code) {
            this.showResult({
                success: false,
                error: 'No code to execute'
            });
            return;
        }

        this.setLoading(true);
        const startTime = Date.now();

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code }),
            });

            const result = await response.json();
            this.handleExecutionResult(result, startTime);
        } catch (error) {
            this.handleExecutionResult({
                success: false,
                error: 'Failed to execute code: ' + error.message
            }, startTime);
        } finally {
            this.setLoading(false);
        }
    }

    handleExecutionResult(result, startTime) {
        const executionTime = Date.now() - startTime;
        this.setLoading(false);
        this.showResult(result);
        this.updateExecutionTime(executionTime);

        // Add to history
        this.executionHistory.push({
            code: this.editor.getValue(),
            result: result,
            timestamp: new Date(),
            executionTime: executionTime
        });
    }

    showResult(result) {
        const resultsDiv = document.getElementById('results');
        
        let html = '';
        let className = '';
        
        if (result.success) {
            className = 'result-success';
            
            if (result.output) {
                html += `<div class="result-output"><strong>Output:</strong>\n${this.escapeHtml(result.output)}</div>\n`;
            }
            
            if (result.result !== undefined) {
                const resultStr = this.formatResult(result.result, result.type);
                html += `<div><strong>Result (${result.type}):</strong>\n${resultStr}</div>`;
            }
        } else {
            className = 'result-error';
            html += `<div><strong>Error:</strong>\n${this.escapeHtml(result.error)}</div>`;
            
            if (result.output) {
                html += `<div class="result-output"><strong>Output:</strong>\n${this.escapeHtml(result.output)}</div>`;
            }
        }
        
        resultsDiv.innerHTML = html;
        resultsDiv.className = `result-content ${className}`;
        
        // Scroll to bottom
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }

    formatResult(result, type) {
        if (result === null) return 'null';
        if (result === undefined) return 'undefined';
        
        if (type === 'object') {
            try {
                return JSON.stringify(result, null, 2);
            } catch (e) {
                return String(result);
            }
        }
        
        return this.escapeHtml(String(result));
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    setLoading(loading) {
        const loadingDiv = document.getElementById('loading');
        const executeBtn = document.getElementById('execute-btn');

        if (loading) {
            loadingDiv.classList.add('show');
            executeBtn.disabled = true;
        } else {
            loadingDiv.classList.remove('show');
            executeBtn.disabled = false;
        }
    }

    updateExecutionTime(time) {
        const timeDiv = document.getElementById('execution-time');
        timeDiv.textContent = `Executed in ${time}ms`;
    }

    clearResults() {
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = '<div style="opacity: 0.7;">Results cleared...</div>';
        resultsDiv.className = 'result-content';
        document.getElementById('execution-time').textContent = '';
    }

    clearEditor() {
        this.editor.setValue('');
        this.editor.focus();
    }

    async getContextInfo() {
        try {
            const response = await fetch('/api/context');
            if (response.ok) {
                const info = await response.json();
                this.updateContextInfo(info);
            }
        } catch (error) {
            console.error('Failed to get context info:', error);
        }
    }

    setLayout(layout) {
        this.currentLayout = layout;
        const panelsContainer = document.getElementById('panels-container');
        const resizer = document.getElementById('resizer');
        const horizontalBtn = document.getElementById('horizontal-btn');
        const verticalBtn = document.getElementById('vertical-btn');
        const sidebarToggle = document.getElementById('sidebar-toggle');

        // Update button states
        horizontalBtn.classList.toggle('active', layout === 'horizontal');
        verticalBtn.classList.toggle('active', layout === 'vertical');

        // Update panels container classes
        panelsContainer.className = `panels-container ${layout}`;

        // Show/hide sidebar toggle based on layout
        if (layout === 'vertical') {
            sidebarToggle.classList.add('show');
            resizer.classList.remove('horizontal');
            resizer.style.cursor = 'col-resize';
        } else {
            sidebarToggle.classList.remove('show');
            // Always show sidebar in horizontal mode
            this.showSidebar();
            resizer.classList.add('horizontal');
            resizer.style.cursor = 'row-resize';
        }

        // Refresh editor to handle layout changes
        setTimeout(() => {
            this.updateEditorHeight();
        }, 100);

        // Save layout preference
        localStorage.setItem('kashf-repl-layout', layout);
    }

    updateEditorHeight() {
        this.editor.refresh();

        if (this.currentLayout === 'vertical') {
            // In vertical mode with CSS Grid, panels automatically have equal height
            // Just calculate the editor height within its panel
            const panelsContainer = document.getElementById('panels-container');
            const containerHeight = panelsContainer.offsetHeight;
            const sectionHeader = document.querySelector('.editor-panel .section-header');
            const headerHeight = sectionHeader ? sectionHeader.offsetHeight : 40;
            const editorHeight = containerHeight - headerHeight - 20; // 20px for padding

            this.editor.setSize(null, `${editorHeight}px`);
        } else {
            // In horizontal mode, let CSS handle the height
            this.editor.setSize(null, 'auto');
        }
    }

    toggleSidebar() {
        this.sidebarVisible = !this.sidebarVisible;
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('sidebar-toggle');

        if (this.sidebarVisible) {
            sidebar.classList.remove('hidden');
            toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        } else {
            sidebar.classList.add('hidden');
            toggleBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        }

        // Refresh editor after sidebar toggle
        setTimeout(() => {
            this.editor.refresh();
        }, 300);

        // Save sidebar state
        localStorage.setItem('kashf-repl-sidebar', this.sidebarVisible);
    }

    showSidebar() {
        this.sidebarVisible = true;
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('sidebar-toggle');

        sidebar.classList.remove('hidden');
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';

        localStorage.setItem('kashf-repl-sidebar', this.sidebarVisible);
    }

    setupResizer() {
        const resizer = document.getElementById('resizer');
        const panelsContainer = document.getElementById('panels-container');
        const editorPanel = panelsContainer.querySelector('.editor-panel');
        const outputPanel = panelsContainer.querySelector('.output-panel');

        let isResizing = false;
        let startX = 0;
        let startY = 0;
        let startEditorSize = 0;

        resizer.addEventListener('mousedown', (e) => {
            isResizing = true;
            this.isResizing = true;

            if (this.currentLayout === 'vertical') {
                startX = e.clientX;
                startEditorSize = editorPanel.offsetWidth;
            } else {
                startY = e.clientY;
                startEditorSize = editorPanel.offsetHeight;
            }

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // Add resizing class for visual feedback
            document.body.classList.add('resizing');
            panelsContainer.classList.add('resizing');
            e.preventDefault();
        });

        const handleMouseMove = (e) => {
            if (!isResizing) return;

            if (this.currentLayout === 'vertical') {
                const deltaX = e.clientX - startX;
                const containerWidth = container.offsetWidth - resizer.offsetWidth;
                const newEditorWidth = Math.max(300, Math.min(containerWidth - 300, startEditorSize + deltaX));
                const newOutputWidth = containerWidth - newEditorWidth;

                editorPanel.style.flex = `0 0 ${newEditorWidth}px`;
                outputPanel.style.flex = `0 0 ${newOutputWidth}px`;
            } else {
                const deltaY = e.clientY - startY;
                const containerHeight = container.offsetHeight - resizer.offsetHeight;
                const newEditorHeight = Math.max(200, Math.min(containerHeight - 200, startEditorSize + deltaY));
                const newOutputHeight = containerHeight - newEditorHeight;

                editorPanel.style.flex = `0 0 ${newEditorHeight}px`;
                outputPanel.style.flex = `0 0 ${newOutputHeight}px`;
            }

            // Refresh editor during resize
            this.updateEditorHeight();
        };

        const handleMouseUp = () => {
            isResizing = false;
            this.isResizing = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);

            // Remove resizing class
            document.body.classList.remove('resizing');
            panelsContainer.classList.remove('resizing');

            // Final editor refresh
            setTimeout(() => {
                this.updateEditorHeight();
            }, 50);
        };

        // Load saved layout preference
        const savedLayout = localStorage.getItem('kashf-repl-layout');
        if (savedLayout && (savedLayout === 'horizontal' || savedLayout === 'vertical')) {
            this.setLayout(savedLayout);
        }

        // Load saved sidebar state
        const savedSidebarState = localStorage.getItem('kashf-repl-sidebar');
        if (savedSidebarState !== null) {
            this.sidebarVisible = savedSidebarState === 'true';
            if (!this.sidebarVisible && this.currentLayout === 'vertical') {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.getElementById('sidebar-toggle');
                sidebar.classList.add('hidden');
                toggleBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            }
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (!this.isResizing) {
                setTimeout(() => {
                    this.updateEditorHeight();
                }, 100);
            }
        });
    }
}

// Example functions
function loadExample(type) {
    const repl = window.kashfREPL;
    
    const examples = {
        help: 'help()',
        basic: `// Basic JavaScript example
console.log('Hello from Kashf REPL!');
const message = 'This is working!';
console.log(message);
return message;`,
        async: `// Async/await example
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

console.log('Starting async operation...');
await delay(1000);
console.log('Async operation completed!');

return 'Async example finished';`,
        nestjs: `// NestJS and Database example
try {
    console.log('App context available:', !!app);

    if (app) {
        // Get configuration service
        const configService = getService('ConfigService');
        console.log('Config service loaded:', !!configService);

        // Try to get database connection
        try {
            const entityManager = getEntityManager();
            console.log('Database connection available:', !!entityManager);

            // Example query (adjust table name as needed)
            const result = await entityManager.query('SELECT 1 as test');
            console.log('Database test query result:', result);

            return 'NestJS and database context working!';
        } catch (dbError) {
            console.warn('Database not available:', dbError.message);
            return 'NestJS context available, but database connection failed';
        }
    } else {
        return 'NestJS context not available - running in basic mode';
    }
} catch (error) {
    console.error('Error:', error.message);
    return 'Error accessing NestJS context';
}`
    };
    
    if (examples[type]) {
        repl.editor.setValue(examples[type]);
        repl.editor.focus();
    }
}

// Layout switching functionality
function setLayout(layout) {
    window.kashfREPL.setLayout(layout);
}

// Sidebar toggle functionality
function toggleSidebar() {
    window.kashfREPL.toggleSidebar();
}

// Global functions for buttons
function executeCode() {
    window.kashfREPL.executeCode();
}

function clearResults() {
    window.kashfREPL.clearResults();
}

function clearEditor() {
    window.kashfREPL.clearEditor();
}

function loadSavedCode() {
    window.kashfREPL.loadFromLocalStorage();
}

function formatCode() {
    window.kashfREPL.formatCode();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kashfREPL = new KashfREPL();
});
